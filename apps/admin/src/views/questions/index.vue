<script setup lang="ts">
import { ref } from 'vue'
import LeftView from './components/left-view/index.vue'
import RightView from './components/right-view/index.vue'
import SelectDrawer from './components/selectDown/index.vue'
import type { ProcessedQuestionData } from './utils/question-data-processor'

defineOptions({
  name: 'Questions',
})

// 题目数据状态
const questionsList = ref<ProcessedQuestionData[]>([])
const currentProgress = ref({ current: 0, total: 0, description: '' })
const isGenerating = ref(false)
const generationError = ref('')

// 处理题目生成事件
function handleQuestionGenerated(data: ProcessedQuestionData) {
  questionsList.value.push(data)
}

function handleGenerationComplete() {
  isGenerating.value = false
  console.log('题目生成完成，共生成', questionsList.value.length, '道题目')
}

function handleGenerationError(error: string) {
  isGenerating.value = false
  generationError.value = error
  console.error('题目生成错误:', error)
}
const showModal = ref(false)
</script>

<template>
  <div class="bg-container-view relative h-full w-full rounded-8px bg-[#F2F6FC]">
    <!-- 背景 -->
    <div class="relative h-full flex rounded-8px">
      <LeftView
        class="mr-12px w-40%"
        @question-generated="handleQuestionGenerated"
        @generation-complete="handleGenerationComplete"
        @generation-error="handleGenerationError"
      />
      <RightView
        class="h-full w-60%"
        :questions-list="questionsList"
        :current-progress="currentProgress"
        :is-generating="isGenerating"
        :generation-error="generationError"
      />
      <NButton type="primary" class="absolute right-12px top-12px" @click="showModal = true">
        测试弹窗
      </NButton>
      <SelectDrawer v-model:is-show-modal="showModal" title="关联知识点" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bg-container-view{
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGa2cc791875d5eb4639bbba2971f330fc.png) 100% no-repeat;
  background-size: 100% 100%;
}
</style>

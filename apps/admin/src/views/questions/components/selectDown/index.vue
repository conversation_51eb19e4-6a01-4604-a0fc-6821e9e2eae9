<script lang="ts" setup>
defineOptions({
  name: 'SelectDraw<PERSON>',
})
const props = defineProps<Props>()
interface Props {
  title: string
}
const isShowModal = defineModel('isShowModal', {
  type: Boolean,
  default: false,
})
</script>

<template>
  <NDrawer v-model:show="isShowModal" width="50%" :auto-focus="false">
    <NDrawerContent :title="props.title" closable :native-scrollbar="false">
      <template #footer>
        <NSpace>
          <NButton>取消</NButton>
          <NButton type="primary">
            确定
          </NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>
